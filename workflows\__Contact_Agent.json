{"name": "🤖Contact Agent", "nodes": [{"parameters": {"model": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "gpt-4o"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-140, 140], "id": "789b640d-a981-43a1-ae88-9dbbd4de92c0", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "BP9v81AwJlpYGStD", "name": "OpenAi account"}}}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appK0rbtvf9e7vt6w", "mode": "list", "cachedResultName": "Contacts", "cachedResultUrl": "https://airtable.com/appK0rbtvf9e7vt6w"}, "table": {"__rl": true, "value": "tbl08JGCfUK1RhXsG", "mode": "list", "cachedResultName": "Contacts", "cachedResultUrl": "https://airtable.com/appK0rbtvf9e7vt6w/tbl08JGCfUK1RhXsG"}, "options": {}}, "type": "n8n-nodes-base.airtableTool", "typeVersion": 2.1, "position": [40, 140], "id": "6b3489a8-75be-461b-a4e4-9592a23a138f", "name": "Get Contacts", "credentials": {"airtableTokenApi": {"id": "UlAGE0msyITVkoCN", "name": "<PERSON>"}}}, {"parameters": {"operation": "upsert", "base": {"__rl": true, "value": "appK0rbtvf9e7vt6w", "mode": "list", "cachedResultName": "Contacts", "cachedResultUrl": "https://airtable.com/appK0rbtvf9e7vt6w"}, "table": {"__rl": true, "value": "tbl08JGCfUK1RhXsG", "mode": "list", "cachedResultName": "Contacts", "cachedResultUrl": "https://airtable.com/appK0rbtvf9e7vt6w/tbl08JGCfUK1RhXsG"}, "columns": {"mappingMode": "defineBelow", "value": {"name": "={{ $fromAI(\"name\") }}", "email": "={{ $fromAI(\"emailAddress\") }}", "phoneNumber": "={{ $fromAI(\"phoneNumber\") }}"}, "matchingColumns": ["name"], "schema": [{"id": "name", "displayName": "name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "email", "displayName": "email", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "phoneNumber", "displayName": "phoneNumber", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtableTool", "typeVersion": 2.1, "position": [200, 140], "id": "a0eb4ad0-4e60-41bd-8854-ad20942453a4", "name": "Add or Update Contact", "credentials": {"airtableTokenApi": {"id": "UlAGE0msyITVkoCN", "name": "<PERSON>"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.query }}", "options": {"systemMessage": "=# Overview\nYou are a contact management assistant. Your responsibilities include looking up contacts, adding new contacts, or updating a contact's information.\n\n**Contact Management**  \n   - Use \"Get Contacts\" to retrieve contact information. \n   - Use \"Add or Update Contact\" to store new contact information or modify existing entries. "}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [-20, -80], "id": "a3b9dae0-1458-4cb1-b17c-9349d41c03b5", "name": "Contact Agent", "onError": "continueErrorOutput"}, {"parameters": {"assignments": {"assignments": [{"id": "4f360190-a717-4a93-8336-d03ea65975d5", "name": "response", "value": "={{ $json.output }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [500, -160], "id": "c33b944e-cb4f-447b-ad1f-5e199ed078ac", "name": "Response"}, {"parameters": {"assignments": {"assignments": [{"id": "4f360190-a717-4a93-8336-d03ea65975d5", "name": "response", "value": "An error occurred. Please try again.", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [500, 20], "id": "2df9e0c0-3f4f-4a06-a36f-f552fe99e2b8", "name": "Try Again"}, {"parameters": {"inputSource": "passthrough"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-240, -80], "id": "ca88c05c-5a68-4a88-b15b-22398fb15d86", "name": "When Executed by Another Workflow"}], "pinData": {}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "Contact Agent", "type": "ai_languageModel", "index": 0}]]}, "Get Contacts": {"ai_tool": [[{"node": "Contact Agent", "type": "ai_tool", "index": 0}]]}, "Add or Update Contact": {"ai_tool": [[{"node": "Contact Agent", "type": "ai_tool", "index": 0}]]}, "Contact Agent": {"main": [[{"node": "Response", "type": "main", "index": 0}], [{"node": "Try Again", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Contact Agent", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "24f13596-516c-4365-b91d-e477ed1c652b", "meta": {"templateCredsSetupCompleted": true, "instanceId": "95e5a8c2e51c83e33b232ea792bbe3f063c094c33d9806a5565cb31759e1ad39"}, "id": "IsSUyrla7wc1cDLE", "tags": []}