{"id": "ZkIH2ygj2BNSfMOh", "meta": {"instanceId": "ac63467607103d9c95dd644384984672b90b1cb03e07edbaf18fe72b2a6c45bb", "templateCredsSetupCompleted": true}, "name": "Dynamic Form with AI", "tags": [], "nodes": [{"id": "5893c244-22b0-4699-a286-0ce121ccc427", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-340, 240], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "1OMpAMAKR9l3eUDI", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "e7e333d4-42e5-4e6a-b78b-a3a45c31f37c", "name": "Clarification Questions", "type": "n8n-nodes-base.form", "position": [1100, -60], "webhookId": "61936e5d-a2d3-447f-bf2f-722be2e1eb17", "parameters": {"options": {}, "defineForm": "json", "jsonOutput": "={{ $json.data }}"}, "typeVersion": 1}, {"id": "4b2bbc17-0e74-499d-ac6f-6c94ce3eb5ee", "name": "Get Basic Information", "type": "n8n-nodes-base.formTrigger", "position": [-880, -60], "webhookId": "5256b332-3d3c-486a-8449-85fa44961bb8", "parameters": {"options": {}, "formTitle": "Get in Touch", "formFields": {"values": [{"fieldLabel": "Name", "placeholder": "<PERSON>", "requiredField": true}, {"fieldLabel": "Company Name", "placeholder": "Company Limited", "requiredField": true}, {"fieldLabel": "Job Title", "placeholder": "CEO", "requiredField": true}, {"fieldType": "email", "fieldLabel": "Email", "placeholder": "<EMAIL>", "requiredField": true}]}}, "typeVersion": 2.2}, {"id": "b2eb9da9-571d-44ee-9944-a787f8d6cd50", "name": "Get Business Overview", "type": "n8n-nodes-base.form", "position": [-640, -60], "webhookId": "16216db0-6150-4ac7-b1f7-7fd6c2eb74c5", "parameters": {"options": {}, "formFields": {"values": [{"fieldType": "textarea", "fieldLabel": "Please describe your current situation and why you are interested in automating with AI", "requiredField": true}]}}, "typeVersion": 1}, {"id": "93c96c45-9512-46c2-9fe0-c4558b93e9d6", "name": "End Form", "type": "n8n-nodes-base.form", "position": [1320, -60], "webhookId": "eb756213-2fae-4b29-85b3-727d3cf53b90", "parameters": {"options": {}, "operation": "completion", "completionTitle": "Form Completed", "completionMessage": "Thank you for answering these questions. We'll be in touch soon!"}, "typeVersion": 1}, {"id": "123b688b-adae-4fe2-85cf-fc066175d96f", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [-120, 240], "parameters": {"jsonSchemaExample": "{\n  \"response\": [\n    {\n      \"question\": \"What is the biggest challenge facing their business at present?\",\n      \"has_been_answered\": false,\n      \"reasoning\": \"put your reason here\"\n    },\n    {\n      \"question\": \"Does the company have any existing automation workflows already in place?\",\n      \"has_been_answered\": true,\n      \"reasoning\": \"put your reason here\"\n    },\n    {\n      \"question\": \"Is the respondent a decision-maker in the business? (This can be inferred from their job title if it indicates a leadership position such as CEO, Founder, Director, etc.)\",\n      \"has_been_answered\": false,\n      \"reasoning\": \"put your reason here\"\n    },\n    {\n      \"question\": \"Which specific business functions or departments are they looking to automate? (Examples: Sales, Marketing, HR, Finance, Customer Service, Supply Chain, etc.)\",\n      \"has_been_answered\": true,\n      \"reasoning\": \"put your reason here\"\n    },\n    {\n      \"question\": \"What does their current IT infrastructure look like?\",\n      \"has_been_answered\": false,\n      \"reasoning\": \"put your reason here\"\n    }\n  ]\n}\n"}, "typeVersion": 1.2}, {"id": "3a2d86a3-62ed-4003-a012-bfdabc9eafc8", "name": "Remove Already Answered Questions", "type": "n8n-nodes-base.filter", "position": [340, -60], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "40bc4f8b-7fd3-4149-af5d-aca71eb9b034", "operator": {"type": "boolean", "operation": "false", "singleValue": true}, "leftValue": "={{ $json.has_been_answered }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "a97d53ae-1649-4809-8793-5e4a815016cb", "name": "Analyse Response", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [-280, -60], "parameters": {"text": "=## Analysis Task\n\nAnalyze the following customer response to the question \"Please describe your current situation and why you are interested in automating with AI.\" \n\nCustomer Information:\n- Job Title: {{ $('Get Basic Information').item.json['Job Title'] }}\n- Response: {{ $json['Please describe your current situation and why you are interested in automating with AI'] }}\n\n## Required Information\nIdentify whether the customer's response clearly addresses each of these critical questions:\n\n1. What specific goals are you looking to achieve with automation?\n2. Does the company have any existing automation workflows already in place?\n3. Is the respondent a decision-maker in the business? (This can be inferred from their job title if it indicates a leadership position such as CEO, Founder, Director, etc.)\n4. Which specific business functions or departments are you looking to automate? (Examples: Sales, Marketing, HR, Finance, Customer Service, Supply Chain, etc.)\n5. What does your current IT infrastructure look like?\n\n## Output Format\nAnalyse each question with whether you believe that the question has already been answered. Go step by step and use reasoning. ", "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.5}, {"id": "12b8cc80-ff5e-4ebd-a72d-2629f743355e", "name": "Split Out Analysis", "type": "n8n-nodes-base.splitOut", "position": [120, -60], "parameters": {"options": {}, "fieldToSplitOut": "output.response"}, "notesInFlow": false, "typeVersion": 1}, {"id": "c28929cf-7590-4e32-be20-f9065920ed80", "name": "Prepare For Form Generation", "type": "n8n-nodes-base.set", "position": [580, -60], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "ae1dbc1e-6005-4b5e-acbe-c3fda6d4413f", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "value": "={{ $json.question }}"}, {"id": "c46276bc-018e-4edb-82e0-f6a4dc9d4953", "name": "requiredField", "type": "boolean", "value": true}, {"id": "b060ed04-a99c-475b-a5b6-6cb5d57ea2ff", "name": "fieldType", "type": "string", "value": "textarea"}]}}, "typeVersion": 3.4}, {"id": "33d55396-e716-41c5-bf25-d0bfcfadf167", "name": "Aggregate For Form Generation", "type": "n8n-nodes-base.aggregate", "position": [840, -60], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "15b39119-08d6-45bf-9323-09fa5b59a64e", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-1660, -300], "parameters": {"width": 700, "height": 780, "content": "# Avoid Asking Redundant Questions with Dynamically Generated Forms using OpenAI \n## Target Audience\nThis workflow has been built for those who require a form to capture as much data as possible as well as the answers to predefined questions, whilst optimising the user experience by avoiding asking redundant questions.\n## Use Case\nWhen creating a form to capture information, it can be useful to give the user an opportunity to input a long answer to a large, open-ended question. We then want to drill down to answer specific questions that we require the answer to. When doing this, we don't want to ask duplicate questions. This particular scenario imagines an AI consultancy capturing leads.\n## What it Does\nThis workflow requires users to input basic information and then answer an open ended question. The specific questions on the next page will only be those that weren't answered in the open-ended question.\n## How it Works\n1. The open-ended question (and relevant basic information) is analysed by an LLM to determine which specific questions have not been answered. Chain-of-thought reasoning is utilised and the output structure is specified with the **Structured Output Parser**.\n2. Those questions that have already been answered are filtered out nodes. The remaining items are then used to generate the last page of the form.\n3. Once the user has filled in the final page of the form, they are shown a form completion page.\n## Next Steps\n- Add additional nodes to send an email to the form owner\n- Add a subsequent LLM call to analyse the form response - those that are qualified should be given the opportunity to book an appointment"}, "typeVersion": 1}, {"id": "e9270776-97f0-4aa4-8797-92a235f7760e", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-940, -300], "parameters": {"width": 480, "height": 140, "content": "## Setup\n1. Add your **OpenAI** credentials\n2. Go to the **Get Basic Information** node and click **Test Step**\n3. Complete the form to test the generic use case\n4. Modify the prompt in **Analyse Response** to fit your use case"}, "typeVersion": 1}, {"id": "6db4d121-f08a-4509-82fd-5d91d1dcbc82", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-940, -140], "parameters": {"color": 7, "width": 480, "height": 240, "content": "## 1. Initial Form Pages\n\n"}, "typeVersion": 1}, {"id": "3ecaaf11-8bc7-415e-8eb3-245f7bcedda7", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-440, -220], "parameters": {"color": 7, "width": 480, "height": 620, "content": "## 2. <PERSON><PERSON><PERSON> Response\n\n"}, "typeVersion": 1}, {"id": "1e2e100e-ac64-45b1-aa3b-318996783a79", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-420, 140], "parameters": {"color": 5, "width": 220, "height": 240, "content": "### Modification\nReplace this sub-node \nto use a different language model"}, "typeVersion": 1}, {"id": "e6f92fbb-7f41-4e02-9316-06e7480c0306", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-300, -160], "parameters": {"color": 5, "width": 300, "height": 240, "content": "### Modification\nModify the prompt to suit your use case"}, "typeVersion": 1}, {"id": "1bcca0c9-a4b3-493f-a188-7ecc00fec36e", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [60, -140], "parameters": {"color": 7, "width": 920, "height": 260, "content": "## 3. Clean Up Analysis\n\n"}, "typeVersion": 1}, {"id": "ffcee0f4-364b-46a5-9deb-cbd005a3b6fc", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1000, -140], "parameters": {"color": 7, "width": 520, "height": 260, "content": "## 4. Generate Final Form Page & End Form\n\n\n"}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "336f5d17-d556-4e9f-8785-9c55c0b5d918", "connections": {"End Form": {"main": [[]]}, "Analyse Response": {"main": [[{"node": "Split Out Analysis", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Analyse Response", "type": "ai_languageModel", "index": 0}]]}, "Split Out Analysis": {"main": [[{"node": "Remove Already Answered Questions", "type": "main", "index": 0}]]}, "Get Basic Information": {"main": [[{"node": "Get Business Overview", "type": "main", "index": 0}]]}, "Get Business Overview": {"main": [[{"node": "Analyse Response", "type": "main", "index": 0}]]}, "Clarification Questions": {"main": [[{"node": "End Form", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Analyse Response", "type": "ai_outputParser", "index": 0}]]}, "Prepare For Form Generation": {"main": [[{"node": "Aggregate For Form Generation", "type": "main", "index": 0}]]}, "Aggregate For Form Generation": {"main": [[{"node": "Clarification Questions", "type": "main", "index": 0}]]}, "Remove Already Answered Questions": {"main": [[{"node": "Prepare For Form Generation", "type": "main", "index": 0}]]}}}