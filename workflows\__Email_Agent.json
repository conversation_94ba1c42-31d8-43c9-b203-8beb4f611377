{"name": "🤖Email Agent", "nodes": [{"parameters": {"model": "gpt-4o", "options": {}}, "id": "c98bcc4d-20a9-4b29-a4aa-f6b6e7bb1f1b", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [560, 680], "credentials": {"openAiApi": {"id": "BP9v81AwJlpYGStD", "name": "OpenAi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "7ab380a2-a8d3-421c-ab4e-748ea8fb7904", "name": "response", "value": "Unable to perform task. Please try again.", "type": "string"}]}, "options": {}}, "id": "0505c1f0-53d1-4988-843f-eb9eac2d7856", "name": "Try Again", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1640, 500]}, {"parameters": {"assignments": {"assignments": [{"id": "39c2f302-03be-4464-a17a-d7cc481d6d44", "name": "=response", "value": "={{$json.output}}", "type": "string"}]}, "options": {}}, "id": "********-bde9-4a13-8d89-68ac6a4305db", "name": "Success", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1640, 320]}, {"parameters": {"promptType": "define", "text": "={{ $json.query }}", "options": {"systemMessage": "=# Overview\nYou are an email management assistant. All emails must be formatted professionally in HTML and signed off as \"Nate.\" \n\n**Email Management Tools**   \n   - Use \"Send Email\" to send emails.  \n   - Use \"Create Draft\" if the user asks for a draft.  \n   - Use \"Get Emails\" to retrieve emails when requested.\n   - Use \"Get Labels\" to retrieve labels.\n   - Use \"Mark Unread\" to mark an email as unread. You must use \"Get Emails\" first so you have the message ID of the email to flag.\n   - Use \"Label Email\" to flag an email. You must use \"Get Emails\" first so you have the message ID of the email to flag. Then you must use \"Get Labels\" so you have the label ID.\n   - Use \"Email Reply\" to reply to an email. You must use \"Get Emails\" first so you have the message ID of the email to reply to.\n\n## Final Notes\n- Here is the current date/time: {{ $now }}"}}, "id": "0f7ba4a7-44b1-41ce-8904-9a78e8e03be4", "name": "Email Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [1040, 400], "onError": "continueErrorOutput"}, {"parameters": {"sendTo": "={{ $fromAI(\"emailAddress\") }}", "subject": "={{ $fromAI(\"subject\") }}", "message": "={{ $fromAI(\"emailBody\") }}", "options": {"appendAttribution": false}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [720, 760], "id": "9e043f46-3e1a-431a-9495-b34e251de785", "name": "Send Email", "webhookId": "86c8c4b1-13bb-4ebe-acb9-30e1d7082d55", "credentials": {"gmailOAuth2": {"id": "MHutgNQIvAz7qMgP", "name": "Gmail account"}}}, {"parameters": {"operation": "getAll", "limit": "={{ $fromAI(\"limit\",\"how many emails the user wants\") }}", "simple": false, "filters": {"sender": "={{ $fromAI(\"sender\",\"who the emails are from\") }}"}, "options": {}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [1360, 860], "id": "fc850981-86fa-4714-a47a-27d5ed2f4944", "name": "Get Emails", "webhookId": "af4b3298-9037-44b0-aa12-2acbfbb5e66f", "credentials": {"gmailOAuth2": {"id": "MHutgNQIvAz7qMgP", "name": "Gmail account"}}}, {"parameters": {"resource": "draft", "subject": "={{ $fromAI(\"subject\") }}", "emailType": "html", "message": "={{ $fromAI(\"emailBody\") }}", "options": {"sendTo": "={{ $fromAI(\"emailAddress\") }}"}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [1200, 880], "id": "c460b943-04a8-4598-9e70-be4f5d4d2303", "name": "Create Draft", "webhookId": "17016bce-d7d7-428a-a56c-f6ea122db8be", "credentials": {"gmailOAuth2": {"id": "MHutgNQIvAz7qMgP", "name": "Gmail account"}}}, {"parameters": {"operation": "reply", "messageId": "={{ $fromAI(\"ID\",\"the message ID\") }}", "message": "={{ $fromAI(\"emailBody\") }}", "options": {"appendAttribution": false}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [880, 820], "id": "500202a6-a9be-45ac-be3d-33e0928fb830", "name": "Email Reply", "webhookId": "114785e6-a859-432b-81b4-c490c1c35b1c", "credentials": {"gmailOAuth2": {"id": "MHutgNQIvAz7qMgP", "name": "Gmail account"}}}, {"parameters": {"resource": "label", "returnAll": true}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [1480, 800], "id": "b05ce6a1-ae44-4d46-a32b-c6d2250794b1", "name": "Get Labels", "webhookId": "9e08b59e-792d-4566-83f1-9263c9ad86ae", "credentials": {"gmailOAuth2": {"id": "MHutgNQIvAz7qMgP", "name": "Gmail account"}}}, {"parameters": {"operation": "addLabels", "messageId": "={{ $fromAI(\"ID\",\"the ID of the message\") }}", "labelIds": "={{ $fromAI(\"labelID\") }}"}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [1040, 860], "id": "88c2635f-5088-4f0c-b5f8-c4a5f73ffc79", "name": "Label Emails", "webhookId": "0e951529-2e6d-40bf-ac40-fc0947e242e2", "credentials": {"gmailOAuth2": {"id": "MHutgNQIvAz7qMgP", "name": "Gmail account"}}}, {"parameters": {"operation": "mark<PERSON><PERSON>n<PERSON>", "messageId": "={{ $fromAI(\"messageID\") }}"}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [1620, 740], "id": "8f3771c3-cf3d-4481-8a6d-4ead60291649", "name": "<PERSON>", "webhookId": "a35af9d8-f67d-4ff9-803f-59ec6356e795", "credentials": {"gmailOAuth2": {"id": "MHutgNQIvAz7qMgP", "name": "Gmail account"}}}, {"parameters": {"inputSource": "passthrough"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [800, 400], "id": "053be115-2ecf-4d22-8f7f-93ad7271bf80", "name": "When Executed by Another Workflow"}], "pinData": {}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "Email Agent", "type": "ai_languageModel", "index": 0}]]}, "Email Agent": {"main": [[{"node": "Success", "type": "main", "index": 0}], [{"node": "Try Again", "type": "main", "index": 0}]]}, "Send Email": {"ai_tool": [[{"node": "Email Agent", "type": "ai_tool", "index": 0}]]}, "Email Reply": {"ai_tool": [[{"node": "Email Agent", "type": "ai_tool", "index": 0}]]}, "Get Emails": {"ai_tool": [[{"node": "Email Agent", "type": "ai_tool", "index": 0}]]}, "Create Draft": {"ai_tool": [[{"node": "Email Agent", "type": "ai_tool", "index": 0}]]}, "Get Labels": {"ai_tool": [[{"node": "Email Agent", "type": "ai_tool", "index": 0}]]}, "Label Emails": {"ai_tool": [[{"node": "Email Agent", "type": "ai_tool", "index": 0}]]}, "Mark Unread": {"ai_tool": [[{"node": "Email Agent", "type": "ai_tool", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Email Agent", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "e76750a7-4129-45a9-83ff-321a6ba6d324", "meta": {"templateCredsSetupCompleted": true, "instanceId": "95e5a8c2e51c83e33b232ea792bbe3f063c094c33d9806a5565cb31759e1ad39"}, "id": "C3hLlOS4O6ZJtVFy", "tags": []}