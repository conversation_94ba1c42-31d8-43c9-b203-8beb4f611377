{"id": "wwvUsosYUyMfpGbB", "meta": {"instanceId": "5b860a91d7844b5237bb51cc58691ca8c3dc5b576f42d4d6bbedfb8d43d58ece", "templateCredsSetupCompleted": true}, "name": "ProspectLens company research", "tags": [], "nodes": [{"id": "fd68acdf-ed1e-4f69-a046-fcdaa626acca", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [720, 400], "parameters": {}, "typeVersion": 1}, {"id": "d4e4875a-e41f-4248-937a-a4658c23eb5e", "name": "Filter", "type": "n8n-nodes-base.filter", "notes": "Only process rows which have empty processed_at field", "position": [1160, 400], "parameters": {"options": {"looseTypeValidation": true}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "5aca0836-4797-41d3-8094-f3a170e5a3c9", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.processed_at }}", "rightValue": ""}]}}, "notesInFlow": true, "typeVersion": 2}, {"id": "e12c1846-dd38-414c-8e2e-8d0834ad8668", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [880, 20], "parameters": {"width": 725, "height": 316.25, "content": "## Company research via Google Sheets and ProspectLens\n\nGet your API key:\nhttps://apiroad.net/marketplace/apis/prospectlens\n\nCopy Google Sheet template:\nhttps://docs.google.com/spreadsheets/d/1S2S18hvfBoFsUgRYPyizH6uv7WwI218frvOqu2bV3wk/edit?gid=0#gid=0"}, "typeVersion": 1}, {"id": "b0385041-92c4-41a4-b0e8-9f2a7cc6fd56", "name": "Save company data into Sheets", "type": "n8n-nodes-base.googleSheets", "position": [2000, 380], "parameters": {"columns": {"value": {"data": "={{ JSON.stringify($json.data).substr(0, 2000) }}", "name": "={{ $json.data.properties.title }}", "funds": "={{ $json.data.info.funding_rounds_summary.funding_total.value }}", "domain": "={{ $('Filter').item.json.domain }}", "traffic": "={{ $json.data.info.semrush_summary.semrush_visits_latest_month }}", "location": "={{ $json.data.info.semrush_location_list[0].location_identifiers[0].value }}", "description": "={{ $json.data.properties.short_description }}", "domain_name": "={{ $json.data.info.company_about_fields.website.hostname }}", "processed_at": "={{ (new Date).toISOString()  }}", "year_founded": "={{ $json.data.info.overview_fields_extended.founded_on.value }}", "funding_round": "={{ $json.data.info.funding_rounds_summary.last_funding_type }}", "last_funding_at": "={{ $json.data.info.funding_rounds_summary.last_funding_at }}"}, "schema": [{"id": "domain", "type": "string", "display": true, "removed": false, "required": false, "displayName": "domain", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "description", "type": "string", "display": true, "removed": false, "required": false, "displayName": "description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "processed_at", "type": "string", "display": true, "required": false, "displayName": "processed_at", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "data", "type": "string", "display": true, "required": false, "displayName": "data", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "domain_name", "type": "string", "display": true, "required": false, "displayName": "domain_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "traffic", "type": "string", "display": true, "required": false, "displayName": "traffic", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "location", "type": "string", "display": true, "removed": false, "required": false, "displayName": "location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "funds", "type": "string", "display": true, "removed": false, "required": false, "displayName": "funds", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "year_founded", "type": "string", "display": true, "removed": false, "required": false, "displayName": "year_founded", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "funding_round", "type": "string", "display": true, "removed": false, "required": false, "displayName": "funding_round", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "last_funding_at", "type": "string", "display": true, "removed": false, "required": false, "displayName": "last_funding_at", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["domain"]}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1X2hKT8cD6fTQUdALg91EwQDCM58YNY4pHe-7rmESzlk/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1X2hKT8cD6fTQUdALg91EwQDCM58YNY4pHe-7rmESzlk", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1X2hKT8cD6fTQUdALg91EwQDCM58YNY4pHe-7rmESzlk/edit?usp=drivesdk", "cachedResultName": "n8n_prospectlens"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "vowsrhMIxy2PRDbH", "name": "Google Sheets account"}}, "typeVersion": 4.4}, {"id": "e048f8d0-57c2-43ac-bedf-1a517b203df3", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "notes": "Used to keep low concurrency (1 thread)", "position": [1400, 380], "parameters": {"options": {}}, "notesInFlow": true, "typeVersion": 3}, {"id": "b6898b5e-dba5-425d-8f9b-d996dcb6cff2", "name": "ProspectLens API call", "type": "n8n-nodes-base.httpRequest", "notes": "ProspectLens API", "onError": "continueErrorOutput", "maxTries": 2, "position": [1680, 380], "parameters": {"url": "=https://prospectlens.apiroad.net/lookup?domain={{ $json.domain }}", "options": {"response": {"response": {}}}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "tcCkO83Qn399Hizf", "name": "APIRoad auth"}}, "notesInFlow": true, "retryOnFail": true, "typeVersion": 4.2}, {"id": "4c625e34-728c-49ae-8e22-4b4a343354cb", "name": "Get all rows from Sheets", "type": "n8n-nodes-base.googleSheets", "position": [940, 400], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1X2hKT8cD6fTQUdALg91EwQDCM58YNY4pHe-7rmESzlk/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1X2hKT8cD6fTQUdALg91EwQDCM58YNY4pHe-7rmESzlk", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1X2hKT8cD6fTQUdALg91EwQDCM58YNY4pHe-7rmESzlk/edit?usp=drivesdk", "cachedResultName": "n8n_prospectlens"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "vowsrhMIxy2PRDbH", "name": "Google Sheets account"}}, "typeVersion": 4.4}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "ea844f9f-c06e-4a0c-98db-a670709c2025", "connections": {"Filter": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "ProspectLens API call", "type": "main", "index": 0}]]}, "ProspectLens API call": {"main": [[{"node": "Save company data into Sheets", "type": "main", "index": 0}], [{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Get all rows from Sheets": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Save company data into Sheets": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get all rows from Sheets", "type": "main", "index": 0}]]}}}