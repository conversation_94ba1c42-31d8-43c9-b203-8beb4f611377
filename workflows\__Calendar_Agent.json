{"name": "🤖Calendar Agent", "nodes": [{"parameters": {"model": "gpt-4o", "options": {}}, "id": "a34e2d84-ae30-4bfe-afa9-23dbd5dd3845", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [740, 540], "credentials": {"openAiApi": {"id": "BP9v81AwJlpYGStD", "name": "OpenAi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "7ab380a2-a8d3-421c-ab4e-748ea8fb7904", "name": "response", "value": "Unable to perform task. Please try again.", "type": "string"}]}, "options": {}}, "id": "ec5518af-86f7-4f41-9682-ddddc621f356", "name": "Try Again", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1660, 380]}, {"parameters": {"assignments": {"assignments": [{"id": "39c2f302-03be-4464-a17a-d7cc481d6d44", "name": "=response", "value": "={{$json.output}}", "type": "string"}]}, "options": {}}, "id": "fc889778-08ca-431e-8109-7133110aa0db", "name": "Success", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1660, 180]}, {"parameters": {"promptType": "define", "text": "={{ $json.query }}", "options": {"systemMessage": "=# Overview\nYou are a calendar assistant. Your responsibilities include creating, getting, and deleting events in the user's calendar.\n\n**Calendar Management Tools**  \n   - Use \"Create Event with Attendee\" when an event includes a participant.  \n   - Use \"Create Event\" for solo events.   \n   - Use \"Get Events\" to fetch calendar schedules when requested.\n   - Use \"Delete Event\" to delete an event. You must use \"Get Events\" first to get the ID of the event to delete.\n   - Use \"Update Event\" to update an event. You must use \"Get Events\" first to get the ID of the event to update.\n\n## Final Notes\nHere is the current date/time: {{ $now }}\nIf a duration for an event isn't specified, assume it will be one hour."}}, "id": "47814b5d-390b-4d4c-b6ec-578075200739", "name": "Calendar Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [980, 280], "onError": "continueErrorOutput"}, {"parameters": {"calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "start": "={{ $fromAI(\"eventStart\") }}", "end": "={{ $fromAI(\"eventEnd\") }}", "additionalFields": {"attendees": ["={{ $fromAI(\"eventAttendeeEmail\") }}"], "summary": "={{ $fromAI(\"eventTitle\") }}"}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [1440, 540], "id": "2d26c039-4756-4a86-b09c-1160b7cd6022", "name": "Create Event with <PERSON><PERSON><PERSON>", "credentials": {"googleCalendarOAuth2Api": {"id": "HYMNtkm0oglf42QP", "name": "Google Calendar account"}}}, {"parameters": {"calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "start": "={{ $fromAI(\"eventStart\") }}", "end": "={{ $fromAI(\"eventEnd\") }}", "additionalFields": {"attendees": [], "summary": "={{ $fromAI(\"eventTitle\") }}"}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [1300, 640], "id": "8bd1e7c7-98a0-4cc1-96e3-cfd2107475a9", "name": "Create Event", "credentials": {"googleCalendarOAuth2Api": {"id": "HYMNtkm0oglf42QP", "name": "Google Calendar account"}}}, {"parameters": {"operation": "getAll", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "timeMin": "={{ $fromAI(\"dayBefore\",\"the day before the date the user requested\") }}", "timeMax": "={{ $fromAI(\"dayAfter\",\"the day after the date the user requested\") }}", "options": {}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [1160, 680], "id": "b148f124-e2b4-4e47-8053-45d03d77ff6e", "name": "Get Events", "credentials": {"googleCalendarOAuth2Api": {"id": "HYMNtkm0oglf42QP", "name": "Google Calendar account"}}}, {"parameters": {"operation": "delete", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "eventId": "={{ $fromAI(\"eventID\") }}", "options": {}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [1020, 660], "id": "923acc0e-85b5-44e6-a063-f1642f5108b3", "name": "Delete Event", "credentials": {"googleCalendarOAuth2Api": {"id": "HYMNtkm0oglf42QP", "name": "Google Calendar account"}}}, {"parameters": {"operation": "update", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "eventId": "={{ $fromAI(\"eventID\") }}", "updateFields": {"end": "={{ $fromAI(\"endTime\") }}", "start": "={{ $fromAI(\"startTime\") }}"}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [880, 620], "id": "41941ae4-9cc7-4c96-8e4f-957804fc8be2", "name": "Update Event", "credentials": {"googleCalendarOAuth2Api": {"id": "HYMNtkm0oglf42QP", "name": "Google Calendar account"}}}, {"parameters": {"inputSource": "passthrough"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [740, 280], "id": "8abc645d-345e-4113-966d-0d3373f4141b", "name": "When Executed by Another Workflow"}], "pinData": {}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "Calendar Agent", "type": "ai_languageModel", "index": 0}]]}, "Calendar Agent": {"main": [[{"node": "Success", "type": "main", "index": 0}], [{"node": "Try Again", "type": "main", "index": 0}]]}, "Create Event with Attendee": {"ai_tool": [[{"node": "Calendar Agent", "type": "ai_tool", "index": 0}]]}, "Create Event": {"ai_tool": [[{"node": "Calendar Agent", "type": "ai_tool", "index": 0}]]}, "Get Events": {"ai_tool": [[{"node": "Calendar Agent", "type": "ai_tool", "index": 0}]]}, "Delete Event": {"ai_tool": [[{"node": "Calendar Agent", "type": "ai_tool", "index": 0}]]}, "Update Event": {"ai_tool": [[{"node": "Calendar Agent", "type": "ai_tool", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Calendar Agent", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "64d1923c-64fc-4d17-b776-cf0528ac9366", "meta": {"templateCredsSetupCompleted": true, "instanceId": "95e5a8c2e51c83e33b232ea792bbe3f063c094c33d9806a5565cb31759e1ad39"}, "id": "0NtlJ41IozGhtFa6", "tags": []}