{"id": "Zp0R3I1dUjZOIz2l", "meta": {"instanceId": "6b3e8c6c30cdfbf06283a3fa57016932c6b4ec959896c5c546ef5865ff697ff1", "templateCredsSetupCompleted": true}, "name": "Sync New Shopify Customers to Odoo Contacts", "tags": [], "nodes": [{"id": "ae072919-4f88-4722-b139-2628e24b89ba", "name": "Filter", "type": "n8n-nodes-base.filter", "position": [-420, -40], "parameters": {"conditions": {"boolean": [{"value1": "={{ $json.existing }}"}]}}, "typeVersion": 1}, {"id": "a36747d5-3381-43b8-9def-e3dbc8942dbd", "name": "Search Odoo Contact", "type": "n8n-nodes-base.odoo", "position": [-800, -40], "parameters": {"limit": 1, "options": {}, "resource": "custom", "operation": "getAll", "filterRequest": {"filter": [{"value": "={{ $('Shopify Trigger').item.json.email }}", "fieldName": "email"}]}, "customResource": "res.partner"}, "credentials": {"odooApi": {"id": "0qIK4Cq1BwOSbxT8", "name": "Odoo **************:8069"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "a52d6acf-e8c2-48cd-b44c-903617c23e9e", "name": "Shopify Trigger", "type": "n8n-nodes-base.shopifyTrigger", "position": [-1060, -40], "webhookId": "30b89f06-e54c-4461-9e1e-9ef7f221e08b", "parameters": {"topic": "customers/create", "authentication": "accessToken"}, "credentials": {"shopifyAccessTokenApi": {"id": "zkXzZzc97XyALfN8", "name": "Evozard - Shopify"}}, "typeVersion": 1}, {"id": "f3023805-dc0b-4745-ab7b-77b2d81137e3", "name": "Create Contact", "type": "n8n-nodes-base.odoo", "position": [-240, -40], "parameters": {"resource": "custom", "customResource": "res.partner", "fieldsToCreateOrUpdate": {"fields": [{"fieldName": "name", "fieldValue": "={{ $('Shopify Trigger').item.json.addresses[0].name }}"}, {"fieldName": "email", "fieldValue": "={{ $('Shopify Trigger').item.json.email }}"}, {"fieldName": "street", "fieldValue": "={{ $('Shopify Trigger').item.json.addresses[0].address1 }}"}, {"fieldName": "street2", "fieldValue": "={{ $('Shopify Trigger').item.json.addresses[0].address2 }}"}, {"fieldName": "city", "fieldValue": "={{ $('Shopify Trigger').item.json.addresses[0].city }}"}, {"fieldName": "zip", "fieldValue": "={{ $('Shopify Trigger').item.json.addresses[0].zip }}"}, {"fieldName": "phone", "fieldValue": "={{ $('Shopify Trigger').item.json.addresses[0].phone }}"}]}}, "credentials": {"odooApi": {"id": "0qIK4Cq1BwOSbxT8", "name": "Odoo **************:8069"}}, "typeVersion": 1, "alwaysOutputData": false}, {"id": "4cef59ef-0ba4-4eee-83b6-27254ffd5974", "name": "Code", "type": "n8n-nodes-base.code", "position": [-600, -40], "parameters": {"mode": "runOnceForEachItem", "jsCode": "\n\nvar contact_detail = $('Shopify Trigger').item.json\nconsole.log('-------contact_detail--------',contact_detail)\nvar existing_contact = $('Search Odoo Contact').item.json\nconsole.log('-------existing_contact--------',existing_contact,existing_contact.valueOf)\nreturn {existing:existing_contact.id ? true:false,contact_detail:contact_detail}\n"}, "typeVersion": 2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "4c04743a-c0c3-4900-9963-3ca05b65908c", "connections": {"Code": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Filter": {"main": [[{"node": "Create Contact", "type": "main", "index": 0}]]}, "Shopify Trigger": {"main": [[{"node": "Search Odoo Contact", "type": "main", "index": 0}]]}, "Search Odoo Contact": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}}}