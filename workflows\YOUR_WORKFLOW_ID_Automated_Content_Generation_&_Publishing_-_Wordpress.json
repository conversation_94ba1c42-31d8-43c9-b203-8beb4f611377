{"id": "YOUR_WORKFLOW_ID", "meta": {"instanceId": "YOUR_INSTANCE_ID", "templateCredsSetupCompleted": true}, "name": "Automated Content Generation & Publishing - Wordpress", "tags": [], "nodes": [{"id": "9cd63357-19dc-4420-baa9-1e1389c7120f", "name": "Create posts on Wordpress", "type": "n8n-nodes-base.wordpress", "position": [1180, 280], "parameters": {"title": "={{ $('Save to Sheet').item.json['title'] }}", "additionalFields": {"status": "publish", "content": "=<img src=\"{{ $('Automated Image Retrieval from Pexels').item.json.photos[0].src.landscape }}\" alt=\"image text\" style=\"width:100%; height:auto;\"><br><br>\n<br><br>\n{{ $node['Save to Sheet'].json['content'] }}"}}, "credentials": {"wordpressApi": {"id": "YOUR_WORDPRESS_CREDENTIAL_ID", "name": "Wordpress account 2"}}, "typeVersion": 1, "alwaysOutputData": false}, {"id": "65f62f19-d10f-4ca1-a853-9cedb3506743", "name": "Processing Delay", "type": "n8n-nodes-base.code", "position": [180, 580], "parameters": {"jsCode": "const delay = Math.floor(Math.random() * (6 * 60 * 60 * 1000)); // random delay 0-6 hour\nreturn {\n  json: {\n    delay: delay,\n    delay_minutes: Math.round(delay / 60000),  // to minutes\n    delay_hours: (delay / 3600000).toFixed(2) // to hours\n  }\n};\n"}, "typeVersion": 2}, {"id": "193d2876-c50e-4b9e-8856-9fd11baa025e", "name": "Random Wait", "type": "n8n-nodes-base.wait", "position": [180, 760], "webhookId": "********-ce9f-497a-80b1-aab29fc9fb69", "parameters": {"amount": "={{$json[\"delay\"] / 1000}}"}, "typeVersion": 1.1}, {"id": "cf510c21-7c19-4e84-a43a-62d170277cdf", "name": "Save to Sheet", "type": "n8n-nodes-base.googleSheets", "position": [780, 280], "parameters": {"columns": {"value": {"title": "={{ $json.message.content.title }}", "content": "={{ $json.message.content.content }}", "Image search keyword": "={{ $json.message.content.keywords.join(\"+\") }}"}, "schema": [{"id": "title", "type": "string", "display": true, "removed": false, "required": false, "displayName": "title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "content", "type": "string", "display": true, "required": false, "displayName": "content", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Image search keyword", "type": "string", "display": true, "required": false, "displayName": "Image search keyword", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "name", "value": "Sheet1"}, "documentId": {"__rl": true, "mode": "url", "value": "YOURDOCUMENT_URL"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "YOUR_GOOGLE_SHEETS_CREDENTIAL_ID", "name": "Google Sheets account_正確"}}, "typeVersion": 4.5}, {"id": "1778f649-c09e-4ef9-b153-4160eed6805c", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-220, 0], "parameters": {"width": 607.************, "height": 892.*************, "content": "## Automated Article Scheduling\n\n**1. Fast Bulk Article Generation**\nQuickly create multiple AI-generated articles.\nEfficiently streamline content creation.\nReduces manual effort while maintaining quality.\n\n**2. Workflow Testing Before Execution**\nManually test the workflow for debugging.\nEnsure each step runs as expected.\nOptimize before full automation.\n\n**3. Automated & Randomized Publishing**\nSchedule posts at predefined intervals.\nIntroduce random delays for a natural posting pattern.\nPrevents overly predictable publishing behavior."}, "typeVersion": 1}, {"id": "6f385e8c-b3e6-4456-9738-e85ea2cbbea1", "name": "1. Auto Start", "type": "n8n-nodes-base.scheduleTrigger", "disabled": true, "position": [180, 20], "parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 1}]}}, "typeVersion": 1.2}, {"id": "6d7712e8-9033-453b-ad52-09f718bcb701", "name": "2. When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "disabled": true, "position": [180, 200], "parameters": {}, "typeVersion": 1}, {"id": "0fd8fe8f-a0d5-42d9-b728-53340c6e4233", "name": "3. Schedule Your Posts", "type": "n8n-nodes-base.scheduleTrigger", "position": [180, 380], "parameters": {"rule": {"interval": [{"field": "weeks", "triggerAtDay": [2, 4, 0], "triggerAtHour": "={{ 12 }}"}]}}, "typeVersion": 1.2}, {"id": "16c26c36-fb8e-4903-a64c-57803fac83b9", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [400, 440], "parameters": {"width": 351.77682676671327, "height": 271.4285686334568, "content": "## AI Content Generating\n\n**Automatic Content & Keyword Generation\n\n- Use your own prompt to start\n- ChatGPT generates full-length articles with structured headings.\n- Extracts relevant image search keywords for visual enhancement.\n- To implement this, add the following prompt (green note) below your workflow:\n"}, "typeVersion": 1}, {"id": "921173fb-ae10-4f88-a1ab-15f063cd623f", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [400, 740], "parameters": {"color": 4, "width": 349.47344203333904, "height": 1277.4269457977707, "content": "(YOUR PROMPT)\n\n**Image Search Keywords (For Visual Alignment)**\n\n- Automatically generates 3-5 English keywords for image searches based on the article content.\n- Keywords should be specific objects, locations, or atmospheres rather than abstract concepts.\n\n**Article Formatting Requirements**\n\n1️⃣ Title (H1): Ensure unique and trend-driven headlines.\n2️⃣ H2 / H3 Subheadings: Structure content in an SEO-optimized format.\n3️⃣ Article Structure (Enhanced Readability)\n\n** Introduction **\n- Go straight to the point, avoiding excessive background.\n- Use question hooks or market trend data to engage readers.\n\n** Core Content **\n- Include at least three knowledge points to ensure depth.\n- Balance short and long sentences for better flow.\n\n** Conclusion **\n- Avoid generic AI-style summaries; instead, provide insights or actionable takeaways.\n- Optionally include a CTA (Call to Action).\n\n** HTML Formatting **\nEnsure the article is properly structured in HTML format:\n- Headings: Use <h1>, <h2>, <h3> appropriately.\n- Paragraphs: Enclose text within <p>.\n- Emphasized Words: Use <strong> to highlight key terms.\n- Lists: Use <ul> and <li> for bullet points.\n\nEnsure a clean, well-structured output instead of plain text.\n\n### **Final JSON Format\nEnsure the output follows this structure:\n\n{\n  \"title\": \"{Generate an H1 title that aligns with market trends, ensures high click-through rates, and follows keyword strategy}\",\n  \"content\": \"{Generate a complete HTML article including H1, H2, H3 headings, paragraphs, lists, etc.}\",\n  \"keywords\": [\"{Image search keyword 1}\", \"{Image search keyword 2}\", \"{Image search keyword 3}\", \"{Image search keyword 4}\", \"{Image search keyword 5}\"]\n}"}, "typeVersion": 1}, {"id": "364b1ee1-4685-4b10-b988-1704dc65592b", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [760, 440], "parameters": {"width": 367.1064142931126, "height": 267.17005729996885, "content": "## Google Sheet Setting\n**You need to set up these in your sheet column** \n- title\n- content\n- image search keyword\n\n**Mapping \"Values to Send\"**\n- {{ $json.message.content.title }}\n- {{ $json.message.content.content }}\n- {{ $json.message.content.keywords.join(\"+\") }}"}, "typeVersion": 1}, {"id": "26876b53-aa27-4e16-991e-c3618e751c17", "name": "Automated Image Retrieval from Pexels", "type": "n8n-nodes-base.httpRequest", "position": [980, 280], "parameters": {"url": "=https://api.pexels.com/v1/search?per_page=1&orientation=landscape&query={{ $json[\"Image search keyword\"] }}\n", "options": {}, "sendQuery": true, "sendHeaders": true, "queryParameters": {"parameters": [{"name": "query", "value": "={{ $json['Image search keyword'] }}"}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "YOUR_PEXELS_API_KEY"}, {"name": "Content-Type", "value": "application/json"}]}}, "typeVersion": 4.2}, {"id": "769638be-ee38-4e40-a508-f998b09ce1f4", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-220, -240], "parameters": {"color": 3, "width": 608.0701163493336, "height": 211.65896369815192, "content": "## Introduction: WordPress automatically publishes posts and inserts the first image\n\nIt is **highly recommended to install the Featured Image from URL (FIFU) plugin** and enable:\n\n**Auto > Set Featured Media Automatically from Content.** before you generate contents."}, "typeVersion": 1}, {"id": "37f3606f-f110-49d2-bcf5-1edc27149fee", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [400, 229.99235545929986], "parameters": {"width": 348.08256103956126, "height": 170.00764454070014, "content": "Add your API credential"}, "typeVersion": 1}, {"id": "2399a40d-4b79-400c-9e96-df7e683fd666", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [760, 228.00611563256007], "parameters": {"width": 150, "height": 170.00764454070008, "content": "Add your API credential"}, "typeVersion": 1}, {"id": "45e479a6-2eea-44a1-9096-9895a18904fd", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [920, 226.01987580582022], "parameters": {"width": 201.97095074533956, "height": 172.00917344884022, "content": "Add your API credential"}, "typeVersion": 1}, {"id": "e0489552-a7b5-4161-9553-95e23605a9d5", "name": "Generate AI Content", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [440, 280], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "messages": {"values": [{"content": "(YOUR PROMPT)\n(YOUR PROMPT)\n\n**Image Search Keywords (For Visual Alignment)**\n\n- Automatically generates 3-5 English keywords for image searches based on the article content.\n- Keywords should be specific objects, locations, or atmospheres rather than abstract concepts.\n\n**Article Formatting Requirements**\n\n1️⃣ Title (H1): Ensure unique and trend-driven headlines.\n2️⃣ H2 / H3 Subheadings: Structure content in an SEO-optimized format.\n3️⃣ Article Structure (Enhanced Readability)\n\n** Introduction **\n- Go straight to the point, avoiding excessive background.\n- Use question hooks or market trend data to engage readers.\n\n** Core Content **\n- Include at least three knowledge points to ensure depth.\n- Balance short and long sentences for better flow.\n\n** Conclusion **\n- Avoid generic AI-style summaries; instead, provide insights or actionable takeaways.\n- Optionally include a CTA (Call to Action).\n\n** HTML Formatting **\nEnsure the article is properly structured in HTML format:\n- Headings: Use <h1>, <h2>, <h3> appropriately.\n- Paragraphs: Enclose text within <p>.\n- Emphasized Words: Use <strong> to highlight key terms.\n- Lists: Use <ul> and <li> for bullet points.\n\nEnsure a clean, well-structured output instead of plain text.\n\n### **Final JSON Format\nEnsure the output follows this structure:\n\n{\n  \"title\": \"{Generate an H1 title that aligns with market trends, ensures high click-through rates, and follows keyword strategy}\",\n  \"content\": \"{Generate a complete HTML article including H1, H2, H3 headings, paragraphs, lists, etc.}\",\n  \"keywords\": [\"{Image search keyword 1}\", \"{Image search keyword 2}\", \"{Image search keyword 3}\", \"{Image search keyword 4}\", \"{Image search keyword 5}\"]\n}"}]}, "jsonOutput": true}, "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "OpenAi account"}}, "typeVersion": 1.6}], "active": false, "pinData": {}, "settings": {"timezone": "Asia/Taipei", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1", "executionTimeout": -1, "saveManualExecutions": true}, "versionId": "YOUR_VERSION_ID", "connections": {"Random Wait": {"main": [[{"node": "Generate AI Content", "type": "main", "index": 0}]]}, "Save to Sheet": {"main": [[{"node": "Automated Image Retrieval from Pexels", "type": "main", "index": 0}]]}, "Processing Delay": {"main": [[{"node": "Random Wait", "type": "main", "index": 0}]]}, "Generate AI Content": {"main": [[{"node": "Save to Sheet", "type": "main", "index": 0}]]}, "3. Schedule Your Posts": {"main": [[{"node": "Processing Delay", "type": "main", "index": 0}]]}, "Automated Image Retrieval from Pexels": {"main": [[{"node": "Create posts on Wordpress", "type": "main", "index": 0}]]}}}