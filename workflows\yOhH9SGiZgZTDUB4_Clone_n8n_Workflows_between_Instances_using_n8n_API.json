{"id": "yOhH9SGiZgZTDUB4", "meta": {"instanceId": "ecc960f484e18b0e09045fd93acf0d47f4cfff25cc212ea348a08ac3aae81850", "templateCredsSetupCompleted": true}, "name": "Clone n8n Workflows between Instances using n8n API", "tags": [{"id": "aw8suPYTKfXDtMZl", "name": "Utility", "createdAt": "2025-02-10T14:41:49.045Z", "updatedAt": "2025-02-10T14:41:49.045Z"}, {"id": "6rb8rVhKZj4t0Kne", "name": "Current", "createdAt": "2025-02-04T18:13:17.427Z", "updatedAt": "2025-02-04T18:13:17.427Z"}], "nodes": [{"id": "9e61140a-2b09-4dab-9a3b-3ca9781410cf", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-80, -260], "parameters": {}, "typeVersion": 1}, {"id": "50fdfb08-0ca1-4bb4-82a6-46b81ef6e3b2", "name": "GET - Workflows", "type": "n8n-nodes-base.n8n", "position": [180, -400], "parameters": {"filters": {}, "requestOptions": {}}, "credentials": {"n8nApi": {"id": "HBPpxcTQs4aNcq4K", "name": "AK n8n original account"}}, "typeVersion": 1}, {"id": "7c1b5530-bc0c-41f8-ac5f-d53c42ea9c44", "name": "CREATE - Workflow", "type": "n8n-nodes-base.n8n", "position": [1200, -160], "parameters": {"operation": "create", "requestOptions": {}, "workflowObject": "={\n  \"name\": \"{{ $json.name }}\",\n  \"nodes\": {{ JSON.stringify($json[\"nodes\"]) }},\n  \"connections\": {{ JSON.stringify($json[\"connections\"] || {}) }}\n}\n"}, "credentials": {"n8nApi": {"id": "0XLL6lxiSB0ORf5Z", "name": "AlexK1919 n8n ent account"}}, "typeVersion": 1}, {"id": "af3a81b1-f09f-4373-b603-657bba8c1776", "name": "n8n - GET - Projects", "type": "n8n-nodes-base.httpRequest", "position": [1400, -160], "parameters": {"url": "https://n8n-ent.alexk1919.com/api/v1/projects", "options": {}, "authentication": "predefinedCredentialType", "nodeCredentialType": "n8nApi"}, "credentials": {"n8nApi": {"id": "0XLL6lxiSB0ORf5Z", "name": "AlexK1919 n8n ent account"}}, "typeVersion": 4.2}, {"id": "852e6236-aafd-4223-bb90-42db4c923a59", "name": "SET Project ID", "type": "n8n-nodes-base.set", "position": [2000, -160], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "6ba45511-cf1b-42e6-b711-b9abd33ed7e3", "name": "data.id", "type": "string", "value": "={{ $json.data.id }}"}]}}, "typeVersion": 3.4}, {"id": "e8dfa94b-82c1-45ee-b87b-f88996569957", "name": "PUT - Workflow in Project", "type": "n8n-nodes-base.httpRequest", "position": [2200, -160], "parameters": {"url": "=https://n8n-ent.alexk1919.com/api/v1/workflows/{{ $('CREATE - Workflow').item.json.id }}/transfer", "method": "PUT", "options": {}, "sendBody": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "destinationProjectId", "value": "={{ $json.data.id }}"}]}, "nodeCredentialType": "n8nApi"}, "credentials": {"n8nApi": {"id": "0XLL6lxiSB0ORf5Z", "name": "AlexK1919 n8n ent account"}}, "typeVersion": 4.2}, {"id": "e705f445-c125-4ce5-aa33-f91c3f1fb2a6", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [1000, -260], "parameters": {"options": {}, "batchSize": 5}, "typeVersion": 3}, {"id": "cec95100-64a0-4d56-986a-1cdeb6063b96", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1740, -300], "parameters": {"color": 3, "width": 220, "content": "### Change the Destination Project by changing the Project Name"}, "typeVersion": 1}, {"id": "b23a6293-a732-42b4-9976-6d3ab750bd44", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [120, -540], "parameters": {"color": 3, "width": 220, "content": "### Change the Source n8n Instance by changing the Credential"}, "typeVersion": 1}, {"id": "a4e2f1f9-dab9-4576-ba66-d36a16a4d82a", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [120, -220], "parameters": {"color": 3, "width": 220, "content": "### Change the Destination n8n Instance by changing the Credential"}, "typeVersion": 1}, {"id": "56997c18-8985-4fdd-b313-de07ee67c6d7", "name": "GET - Destination Workflows", "type": "n8n-nodes-base.n8n", "position": [180, -80], "parameters": {"limit": 200, "filters": {}, "returnAll": false, "requestOptions": {"batching": {"batch": {}}}}, "credentials": {"n8nApi": {"id": "0XLL6lxiSB0ORf5Z", "name": "AlexK1919 n8n ent account"}}, "typeVersion": 1}, {"id": "c9bb6d33-a674-416b-916d-56352b74a603", "name": "Code", "type": "n8n-nodes-base.code", "disabled": true, "position": [800, -260], "parameters": {"jsCode": "const data = $json;\nconsole.log(\"Merged Output:\", data);\nreturn [data];\n"}, "typeVersion": 2}, {"id": "3357623e-e41a-4441-aba4-4593cbc77bdd", "name": "Split Out Workflows", "type": "n8n-nodes-base.splitOut", "position": [380, -400], "parameters": {"include": "allOtherFields", "options": {}, "fieldToSplitOut": "id"}, "typeVersion": 1}, {"id": "b1a2d1df-4957-491d-9c8d-347c4c5197f1", "name": "Split Out Workflows1", "type": "n8n-nodes-base.splitOut", "position": [380, -80], "parameters": {"include": "allOtherFields", "options": {}, "fieldToSplitOut": "id"}, "typeVersion": 1}, {"id": "f0f4c869-f171-4019-a081-9c232851f0a9", "name": "Merge Workflows", "type": "n8n-nodes-base.merge", "position": [600, -260], "parameters": {"mode": "combineBySql", "query": "SELECT input1.name, input1.createdAt, input1.updatedAt, input1.active, input1.nodes, input1.settings, input1.connections, input1.pinData, input1.tags, input1.id\nFROM input1\nLEFT JOIN input2 \nON input1.name = input2.name\nWHERE input2.name IS NULL\n"}, "typeVersion": 3}, {"id": "f69c8787-7590-4011-a36f-36c9192089cf", "name": "Split Out Projects", "type": "n8n-nodes-base.splitOut", "position": [1600, -160], "parameters": {"include": "allOtherFields", "options": {}, "fieldToSplitOut": "data"}, "typeVersion": 1}, {"id": "7c8f8957-f80c-4250-96fb-f86032e3aacc", "name": "Filter Project", "type": "n8n-nodes-base.filter", "position": [1800, -160], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "74ca2595-359b-4e17-988b-799306f748cf", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.name }}", "rightValue": "z Original n8n Workflows from AlexK1919"}]}}, "typeVersion": 2.2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "0178ee38-a035-40e7-9a62-34dfdf6f0b93", "connections": {"Code": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Filter Project": {"main": [[{"node": "SET Project ID", "type": "main", "index": 0}]]}, "SET Project ID": {"main": [[{"node": "PUT - Workflow in Project", "type": "main", "index": 0}]]}, "GET - Workflows": {"main": [[{"node": "Split Out Workflows", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "CREATE - Workflow", "type": "main", "index": 0}]]}, "Merge Workflows": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "CREATE - Workflow": {"main": [[{"node": "n8n - GET - Projects", "type": "main", "index": 0}]]}, "Split Out Projects": {"main": [[{"node": "Filter Project", "type": "main", "index": 0}]]}, "Split Out Workflows": {"main": [[{"node": "Merge Workflows", "type": "main", "index": 0}]]}, "Split Out Workflows1": {"main": [[{"node": "Merge Workflows", "type": "main", "index": 1}]]}, "n8n - GET - Projects": {"main": [[{"node": "Split Out Projects", "type": "main", "index": 0}]]}, "PUT - Workflow in Project": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "GET - Destination Workflows": {"main": [[{"node": "Split Out Workflows1", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "GET - Workflows", "type": "main", "index": 0}, {"node": "GET - Destination Workflows", "type": "main", "index": 0}]]}}}