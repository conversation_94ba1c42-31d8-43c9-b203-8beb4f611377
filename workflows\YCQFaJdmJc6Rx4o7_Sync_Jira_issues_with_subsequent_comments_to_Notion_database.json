{"id": "YCQFaJdmJc6Rx4o7", "meta": {"instanceId": "0c0787ab1a9ebbb0967650f7b4012417acdd61c2fa7c9e119981847e2fc8b09c"}, "name": "Sync Jira issues with subsequent comments to Notion database", "tags": [{"id": "24", "name": "n8n team", "createdAt": "2023-02-28T11:17:04.513Z", "updatedAt": "2023-02-28T11:17:04.513Z"}], "nodes": [{"id": "3f36dc12-5011-4786-aa21-f20ba72944df", "name": "Create database page", "type": "n8n-nodes-base.notion", "position": [460, 460], "parameters": {"title": "={{$node[\"On issues created/updated/deleted\"].json[\"issue\"][\"fields\"][\"summary\"]}}", "options": {}, "resource": "databasePage", "databaseId": "e3503d88-accb-4ddb-aa45-f962cb03e729", "propertiesUi": {"propertyValues": [{"key": "Issue Key|rich_text", "textContent": "={{$node[\"On issues created/updated/deleted\"].json[\"issue\"][\"key\"]}}"}, {"key": "Issue ID|number", "numberValue": "={{parseInt($node[\"On issues created/updated/deleted\"].json[\"issue\"][\"id\"])}}"}, {"key": "Link|url", "urlValue": "=https://n8n-io.atlassian.net/browse/{{$node[\"On issues created/updated/deleted\"].json[\"issue\"][\"key\"]}}", "ignoreIfEmpty": true}, {"key": "Status|select", "selectValue": "={{$node[\"Lookup table\"].json[\"Status ID\"]}}"}]}}, "credentials": {"notionApi": {"id": "XNjSmr171NUO17TK", "name": "REPLACE ME"}}, "typeVersion": 2}, {"id": "2d13b713-dd3d-48aa-a550-fe8db1e7aafd", "name": "Note", "type": "n8n-nodes-base.stickyNote", "position": [660, 460], "parameters": {"width": 232.65822784810126, "height": 137.9746835443038, "content": "### `IF` & `Switch` nodes\nThese conditional nodes (`IF` and `Switch`) determine which Notion [**CRUD**](https://www.sumologic.com/glossary/crud/) operations will be performed."}, "typeVersion": 1}, {"id": "374761f7-9299-41a3-8bb3-25434f4f9eaf", "name": "Find database page", "type": "n8n-nodes-base.notion", "position": [660, 660], "parameters": {"options": {}, "resource": "databasePage", "operation": "getAll", "returnAll": true, "databaseId": "e3503d88-accb-4ddb-aa45-f962cb03e729", "filterJson": "={{$node[\"Create custom Notion filters\"].json[\"notionfilter\"]}}", "filterType": "json"}, "credentials": {"notionApi": {"id": "XNjSmr171NUO17TK", "name": "REPLACE ME"}}, "typeVersion": 2}, {"id": "159db4ca-c8da-439a-aa44-63527c7b9dcd", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [860, 660], "parameters": {"rules": {"rules": [{"value2": "jira:issue_updated"}, {"output": 1, "value2": "jira:issue_deleted"}]}, "value1": "={{$node[\"On issues created/updated/deleted\"].json[\"webhookEvent\"]}}", "dataType": "string"}, "typeVersion": 1}, {"id": "080fb157-e160-4bf0-9348-05eabee60f9f", "name": "IF", "type": "n8n-nodes-base.if", "position": [240, 560], "parameters": {"conditions": {"string": [{"value1": "={{$node[\"On issues created/updated/deleted\"].json[\"webhookEvent\"]}}", "value2": "jira:issue_created"}]}}, "typeVersion": 1}, {"id": "3ec2a130-251d-4d28-8dc3-ca31f528f90e", "name": "Delete issue", "type": "n8n-nodes-base.notion", "position": [1080, 760], "parameters": {"pageId": "={{ $node[\"Find database page\"].json[\"id\"] }}", "operation": "archive"}, "credentials": {"notionApi": {"id": "XNjSmr171NUO17TK", "name": "REPLACE ME"}}, "typeVersion": 2}, {"id": "5a23919a-ee95-4935-b619-5eb0b486eef7", "name": "On issues created/updated/deleted", "type": "n8n-nodes-base.<PERSON>ra<PERSON><PERSON><PERSON>", "position": [-160, 560], "webhookId": "042e0fd3-9776-4c23-9f0d-dc032ef22744", "parameters": {"events": ["jira:issue_created", "jira:issue_deleted", "jira:issue_updated"], "additionalFields": {}}, "credentials": {"jiraSoftwareCloudApi": {"id": "xZbqpSTMv8IjtS5Y", "name": "REPLACE ME"}}, "typeVersion": 1}, {"id": "6d3bbfce-cbfc-4590-827b-4ec1eb5c11b6", "name": "Lookup table", "type": "n8n-nodes-base.code", "position": [40, 560], "parameters": {"jsCode": "/* Lookup table for the statuses in Jira. You can find the status IDs by\n   following the instructions provided at this link:\n   https://community.atlassian.com/t5/Jira-Service-Management/How-do-************************-that-show-the-associated-status/qaq-p/1803682\n*/\nvar lookup = {\n    \"To Do\": \"To do\",\n    \"In Progress\": \"In progress\",\n    \"Done\": \"Done\"\n};\n\n\n\nnew_items = [];\n\nfor (item of $items(\"On issues created/updated/deleted\")) {\n  console.log(item.json[\"Status\"]);\n  // instantiate a new variable for status\n  var issue_status = item.json[\"issue\"][\"fields\"][\"status\"][\"name\"];\n  // check if the status is in the lookup table\n  if (issue_status in lookup) {\n    // if it is, then add the status ID to the new_items array\n    new_items.push({\n      \"Status ID\": lookup[issue_status]\n    });\n  }\n}\n\nreturn new_items;"}, "typeVersion": 2}, {"id": "bdc966ce-16bf-47de-aba3-fcd0f912f95f", "name": "Create custom Notion filters", "type": "n8n-nodes-base.code", "position": [460, 660], "parameters": {"jsCode": "const new_items = [];\nfor (item of $items(\"On issues created/updated/deleted\")) {\n\n  // do not process this item if action is created\n  if (item.json[\"webhookEvent\"] == \"jira:issue_created\") {\n    continue;\n  }\n\n  // build the output template\n  var new_item = {\n    \"json\": {\n      \"notionfilter\": \"\"\n    }\n  };\n  new_item = JSON.stringify(new_item);\n  new_item = JSON.parse(new_item);\n  new_items.push(new_item);\n\n  // create Notion filter to find specific database page by issue ID\n  notionfilter = {\n    or: [],\n  }\n\n  const filter = {\n    property: 'Issue ID',\n    number: {\n      equals: parseInt(item.json[\"issue\"][\"id\"])\n    }\n  }\n  notionfilter[\"or\"].push(filter);\n\n  new_item.json.notionfilter = JSON.stringify(notionfilter); \n}\n\nreturn new_items;"}, "typeVersion": 2}, {"id": "f92157a9-1a63-4907-87c8-0b54c3b0ac8e", "name": "Update issue", "type": "n8n-nodes-base.notion", "position": [1080, 560], "parameters": {"pageId": "={{ $node[\"Find database page\"].json[\"id\"] }}", "options": {}, "resource": "databasePage", "operation": "update", "propertiesUi": {"propertyValues": [{"key": "Title|title", "title": "={{$node[\"On issues created/updated/deleted\"].json[\"issue\"][\"fields\"][\"summary\"]}}"}, {"key": "Status|select", "selectValue": "={{$node[\"Lookup table\"].json[\"Status ID\"]}}"}]}}, "credentials": {"notionApi": {"id": "XNjSmr171NUO17TK", "name": "REPLACE ME"}}, "typeVersion": 2}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "490138aa-d92d-439a-b7bb-d6d00a9fab86", "connections": {"IF": {"main": [[{"node": "Create database page", "type": "main", "index": 0}], [{"node": "Create custom Notion filters", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Update issue", "type": "main", "index": 0}], [{"node": "Delete issue", "type": "main", "index": 0}]]}, "Lookup table": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "Find database page": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Create custom Notion filters": {"main": [[{"node": "Find database page", "type": "main", "index": 0}]]}, "On issues created/updated/deleted": {"main": [[{"node": "Lookup table", "type": "main", "index": 0}]]}}}