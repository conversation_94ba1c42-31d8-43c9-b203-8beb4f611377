{"id": "", "meta": {"instanceId": "", "templateCredsSetupCompleted": true}, "name": "piepdrive-test", "tags": [], "nodes": [{"id": "b2838678-c796-4c99-a3da-a2cd1b42ea97", "name": "Pipedrive Trigger - An Organization is created", "type": "n8n-nodes-base.pipedriveTrigger", "position": [820, 380], "webhookId": "f5de09a8-6601-4ad5-8bc8-9b3f4b83e997", "parameters": {"action": "added", "object": "organization"}, "credentials": {"pipedriveApi": {"id": "", "name": "Pipedrive Connection"}}, "typeVersion": 1}, {"id": "5aa05d79-b2fa-4040-b4ca-cad83adf2798", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-20, 120], "parameters": {"width": 656.3637637842876, "height": 1455.9537026322007, "content": "# Enrich Pipedrive's Organization Data with GPT-4o When an Organization is Created in Pipedrive\n\nThis workflow **enriches a Pipedrive organization's data by adding a note to the organization object in Pipedrive**. It assumes there is a custom \"website\" field in your Pipedrive setup, as data will be scraped from this website to generate a note using OpenAI.\n\n## ⚠️ Disclaimer\n**These workflows use a scraping API. Before using it, ensure you comply with the regulations regarding web scraping in your country or state**.\n\n## Important Notes\n- The OpenAI model used is GPT-4o, chosen for its large input token context capacity. However, it is also **the most expensive option**, you should take cost into consideration.\n\n- The system prompt in the OpenAI Node generates output with relevant information, but feel free to improve or **modify it according to your needs**.\n\n## **How It Works**\n\n### Node 1: `Pipedrive Trigger - An Organization is Created`\nThis is the trigger of the workflow. When **an organization object is created in Pipedrive**, this node is triggered and retrieves the data. Make sure you have a \"website\" custom field (the name of the field in the n8n node will appear as a random ID and not with the Pipedrive custom field name).\n\n### Node 2: `ScrapingBee - Get Organization's Website's Homepage Content`\nThis node **scrapes the content** from the URL of the website associated with the **Pipedrive Organization** created in Node 1. The workflow uses the [ScrapingBee](https://www.scrapingbee.com/) API, but you can use any preferred API or simply the HTTP request node in n8n.\n\n### Node 3: `OpenAI - Message GPT-4o with Scraped Data`\nThis node sends HTML-scraped data from the previous node to the **OpenAI GPT-4 model**. The system prompt instructs the model to **extract company data**, such as products or services offered and competitors (if known by the model), and format it as HTML for optimal use in a Pipedrive Note.\n\n### Node 4: `Pipedrive - Create a Note with OpenAI Output`\nThis node **adds a Note to the Organization created in Pipedrive** using the OpenAI node output. The Note will include the company description, target market, selling products, and competitors (if GPT-4 was able to determine them).\n\n### Node 5 & 6: `HTML To Markdown` & `Code - Markdown to Slack Markdown`\nThese two nodes **format the HTML output to Slack Markdown**.\n\nThe Note created in Pipedrive is in HTML format, **as specified by the System Prompt of the OpenAI Node**. To send it to Slack, it needs to be converted to Markdown and then to Slack-specific Markdown.\n\n### Node 7: `Slack - Notify`\nThis node **sends a message in Slack containing the Pipedrive Organization Note** created with this workflow.\n"}, "typeVersion": 1}, {"id": "47ee8bfb-2f9d-4790-a929-1533215d6746", "name": "Pipedrive - Create a Note with OpenAI output", "type": "n8n-nodes-base.pipedrive", "position": [1640, 380], "parameters": {"content": "={{ $json.message.content }}", "resource": "note", "additionalFields": {"org_id": "={{ $('Pipedrive Trigger - An Organization is created').item.json.meta.id }}"}}, "credentials": {"pipedriveApi": {"id": "", "name": "Pipedrive Connection"}}, "typeVersion": 1}, {"id": "7783b531-0469-4bee-868e-4b26a1bb41ba", "name": "Code - Markdown to <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.code", "position": [2080, 380], "parameters": {"jsCode": "const inputMarkdown = items[0].json.data;\n\nfunction convertMarkdownToSlackFormat(markdown) {\n    let slackFormatted = markdown;\n    \n    // Convert headers\n    slackFormatted = slackFormatted.replace(/^# (.*$)/gim, '*$1*');\n    slackFormatted = slackFormatted.replace(/^## (.*$)/gim, '*$1*');\n    \n    // Convert unordered lists\n    slackFormatted = slackFormatted.replace(/^\\* (.*$)/gim, '➡️ $1');\n    \n    // Convert tables\n    const tableRegex = /\\n\\|.*\\|\\n\\|.*\\|\\n((\\|.*\\|\\n)+)/;\n    const tableMatch = slackFormatted.match(tableRegex);\n    if (tableMatch) {\n        const table = tableMatch[0];\n        const rows = table.split('\\n').slice(3, -1);\n        const formattedRows = rows.map(row => {\n            const columns = row.split('|').slice(1, -1).map(col => col.trim());\n            return `*${columns[0]}*: ${columns[1]}`;\n        }).join('\\n');\n        slackFormatted = slackFormatted.replace(table, formattedRows);\n    }\n    \n    return slackFormatted;\n}\n\nconst slackMarkdown = convertMarkdownToSlackFormat(inputMarkdown);\nconsole.log(slackMarkdown);\n\n// Return data\nreturn [{ slackFormattedMarkdown: slackMarkdown }];\n"}, "typeVersion": 2}, {"id": "cf2b02df-07e8-4ebb-ba3d-bfd294dcfab0", "name": "Scrapingbee - Get Organization's URL content", "type": "n8n-nodes-base.httpRequest", "position": [1040, 380], "parameters": {"url": "https://app.scrapingbee.com/api/v1", "options": {}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "api_key", "value": "<YOUR_SCRAPINGBEE_API_KEY>"}, {"name": "url", "value": "={{ $json.current.<random_api_id_custom_website_field> }}"}, {"name": "render_js", "value": "false"}]}}, "typeVersion": 4.2}, {"id": "906d44f0-7582-4742-9fd8-4c8dfba918e0", "name": "HTML To Markdown", "type": "n8n-nodes-base.markdown", "position": [1860, 380], "parameters": {"html": "={{ $json.content }}", "options": {}}, "typeVersion": 1}, {"id": "8c1a5d64-4f38-4f9e-8878-443f750206b7", "name": "Slack - Notify ", "type": "n8n-nodes-base.slack", "position": [2300, 380], "parameters": {"text": "=*New Organizaton {{ $('Pipedrive Trigger - An Organization is created').item.json.current.name }} created on Pipedrive* :\n\n\n {{ $json.slackFormattedMarkdown }}", "select": "channel", "channelId": {"__rl": true, "mode": "list", "value": "", "cachedResultName": "pipedrive-notification"}, "otherOptions": {}, "authentication": "oAuth2"}, "credentials": {"slackOAuth2Api": {"id": "", "name": "Slack Connection"}}, "typeVersion": 2.2}, {"id": "2414a5d3-1d4b-447b-b401-4b6f823a0cf9", "name": "OpenAI - Message GPT-4o with Scraped Data", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1260, 380], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "options": {}, "messages": {"values": [{"content": "={{ $json.data }}"}, {"role": "system", "content": "You're an assistant that summarizes website content for CRM entries. The user will provide HTML content from a company's website. Your task is to analyze the HTML content and create a concise summary that includes:\n\n1. A brief description of the company's services or products.\n2. Any information about the company's target market or customer base.\n3. Key points about the company's unique selling propositions or competitive advantages.\n4. Based on the provided information, suggest potential competitors if you know any.\n\nFormat your response as HTML.\n\nExample response :\n\n    <h1>Company Description</h1>\n    <p>Company1 specializes in services related to electric vehicles. The company focuses on providing resources and information about electric car chargers, battery life, different car brands, and the environmental impact of electric vehicles.</p>\n\n    <h2>Target Market</h2>\n    <p>The target market for Company1 includes electric vehicle owners and potential buyers who are interested in making the shift from traditional fossil fuel vehicles to electric cars. The company also targets environmentally conscious consumers who are looking for sustainable mobility solutions.</p>\n\n    <h2>Unique Selling Propositions</h2>\n    <ul>\n        <li>Comprehensive information about electric vehicle charging solutions, including how to install home charging stations.</li>\n        <li>Detailed articles on the advantages of electric vehicles such as ecology and reliability.</li>\n        <li>Educational resources on the autonomy and battery life of different electric car models.</li>\n        <li>Insights into premier electric vehicle brands.</li>\n    </ul>\n\n    <h2>Potential Competitors</h2>\n    <table border=\"1\">\n        <tr>\n            <th>Competitor Name</th>\n            <th>Website</th>\n        </tr>\n        <tr>\n            <td>Competitor1</td>\n            <td><a href=\"https://www.example1.com\">https://www.example1.com</a></td>\n        </tr>\n        <tr>\n            <td>Competitor2</td>\n            <td><a href=\"https://www.example2.com\">https://www.example2.com</a></td>\n        </tr>\n        <tr>\n            <td>Competitor3</td>\n            <td><a href=\"https://www.example3.com\">https://www.example3.com</a></td>\n        </tr>\n        <tr>\n            <td>Competitor4</td>\n            <td><a href=\"https://www.example4.com\">https://www.example4.com</a></td>\n        </tr>\n    </table>\n"}]}}, "credentials": {"openAiApi": {"id": "", "name": "OpenAi Connection"}}, "typeVersion": 1.3}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "", "connections": {"HTML To Markdown": {"main": [[{"node": "Code - Markdown to <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Code - Markdown to Slack Markdown": {"main": [[{"node": "Slack - Notify ", "type": "main", "index": 0}]]}, "OpenAI - Message GPT-4o with Scraped Data": {"main": [[{"node": "Pipedrive - Create a Note with OpenAI output", "type": "main", "index": 0}]]}, "Pipedrive - Create a Note with OpenAI output": {"main": [[{"node": "HTML To Markdown", "type": "main", "index": 0}]]}, "Scrapingbee - Get Organization's URL content": {"main": [[{"node": "OpenAI - Message GPT-4o with Scraped Data", "type": "main", "index": 0}]]}, "Pipedrive Trigger - An Organization is created": {"main": [[{"node": "Scrapingbee - Get Organization's URL content", "type": "main", "index": 0}]]}}}