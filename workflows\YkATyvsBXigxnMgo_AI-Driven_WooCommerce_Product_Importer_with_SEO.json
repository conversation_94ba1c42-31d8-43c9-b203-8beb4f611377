{"id": "YkATyvsBXigxnMgo", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462", "templateCredsSetupCompleted": true}, "name": "AI-Driven WooCommerce Product Importer with SEO", "tags": [], "nodes": [{"id": "aa2d7a06-6e8d-4abc-ab5f-53e9698b1655", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-360, -340], "parameters": {}, "typeVersion": 1}, {"id": "1f2465ab-90fa-44f1-8c06-58b1436dccd0", "name": "OpenRouter <PERSON>", "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "position": [180, 160], "parameters": {"model": "google/gemini-2.0-flash-exp:free", "options": {}}, "credentials": {"openRouterApi": {"id": "pb06rfB4xmxzVe3Q", "name": "OpenRouter"}}, "typeVersion": 1}, {"id": "29832953-a3e1-481d-a8bb-238bbf3c736c", "name": "SEO Expert", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [200, -60], "parameters": {"text": "=Create metatitle and metadescription in the language of the following product:\n\n- Title: {{ $('Create product').item.json.name }}\n- Category: {{ JSON.stringify($('Create product').item.json.categories) }}\n- Short Description: {{ $('Create product').item.json.short_description }}\n- Description: {{ $('Create product').item.json.description }}", "messages": {"messageValues": [{"message": "=You are a SEO expert specialized in creating optimized meta tags. Your task is to analyze the provided content and generate:\n\n1. A meta title of maximum 60 characters that:\n   - Includes the main keyword in a strategic position\n   - Is engaging and encourages clicks\n   - Accurately reflects the page content\n   - Uses clear and direct language\n   - Avoids keyword stuffing\n\n2. A meta description of maximum 160 characters that:\n   - Provides an engaging summary of the content\n   - Includes an appropriate call-to-action\n   - Contains the main keyword and relevant variations\n   - Is grammatically correct and flows well\n   - Maintains consistency with the meta title\n\nANALYSIS PROCESS:\n1. Carefully read the provided content\n2. Identify:\n   - Main topic\n   - Primary and related keywords\n   - Search intent\n   - Unique selling proposition\n   - Target audience\n\n3. Formulate meta tags that:\n   - Maximize CTR\n   - Respect character limits\n   - Are SEO optimized\n   - Reflect the content\n   - Don't insert placeholder\n\nREQUIRED OUTPUT:\nProvide meta tags in the required format\n\nVALIDATION CRITERIA:\n- Verify that the meta title doesn't exceed 60 characters\n- Verify that the meta description doesn't exceed 160 characters\n- Check that both contain the main keyword\n- Ensure the language is persuasive and action-oriented\n- Confirm that meta tags are consistent with the content\n\nIMPORTANT:\n- Don't use excessive punctuation\n- Avoid using special characters unless necessary\n- Don't duplicate information between title and description\n- Maintain a professional yet accessible tone\n- Ensure content is unique and not duplicated\n\nRemember: the goal is to create meta tags that effectively balance SEO optimization and user appeal, maximizing the potential click-through rate in search results."}]}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.5}, {"id": "e61c4d07-e535-404d-a58e-1676e043d1cb", "name": "Get products", "type": "n8n-nodes-base.googleSheets", "position": [-120, -340], "parameters": {"options": {"returnFirstMatch": false}, "filtersUI": {"values": [{"lookupColumn": "DONE"}]}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1vNkSgWHsgYDCusD-xKSrQg64hd7WvOjQmqdB2NdVFG4/edit#gid=0", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "1vNkSgWHsgYDCusD-xKSrQg64hd7WvOjQmqdB2NdVFG4", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1vNkSgWHsgYDCusD-xKSrQg64hd7WvOjQmqdB2NdVFG4/edit?usp=drivesdk", "cachedResultName": "Create WooCommerce products"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "a3ca44a3-a270-4339-aae0-5e7963a9d695", "name": "Map categories", "type": "n8n-nodes-base.code", "position": [400, -320], "parameters": {"jsCode": "for (const item of $input.all()) {\n  if (item.json.CATEGORY && typeof item.json.CATEGORY === 'string') {\n    item.json.CATEGORY = item.json.CATEGORY\n      .split(',')\n      .map(id => parseInt(id))\n      .filter(id => !isNaN(id));\n  }\n}\n\nreturn $input.all();\n"}, "typeVersion": 2}, {"id": "fe36f50a-1197-4f8a-a02f-70687e5049c8", "name": "Create product", "type": "n8n-nodes-base.wooCommerce", "position": [680, -320], "parameters": {"name": "={{ $json.TITLE }}", "imagesUi": {"imagesValues": [{"alt": "={{ $json.TITLE }}", "src": "={{ $json.IMAGE }}", "name": "={{ $json.TITLE }}"}]}, "resource": "product", "operation": "create", "metadataUi": {}, "dimensionsUi": {}, "additionalFields": {"sku": "={{ $json.SKU }}", "type": "simple", "salePrice": "={{ $json[\"SALE PRICE\"] }}", "taxStatus": "taxable", "categories": "={{ $json.CATEGORY }}", "description": "={{ $json.DESCRIPTION }}", "manageStock": true, "stockStatus": "instock", "regularPrice": "={{ $json[\"REGULAR PRICE\"] }}", "stockQuantity": "={{ $json[\"STOCK QTY\"] }}", "shortDescription": "={{ $json[\"SHORT DESCRIPTION\"] }}", "catalogVisibility": "visible"}}, "credentials": {"wooCommerceApi": {"id": "vYYrjB5kgHQ0XByZ", "name": "WooCommerce (wp.test.7hype.com)"}}, "typeVersion": 1}, {"id": "5f70ca85-45c0-483e-ae01-364d5fedb9f8", "name": "Creation done", "type": "n8n-nodes-base.googleSheets", "position": [940, -320], "parameters": {"columns": {"value": {"ID": "={{ $json.id }}", "DONE": "x", "PERMALINK": "={{ $json.permalink }}", "row_number": "={{ $('Map categories').item.json.row_number }}"}, "schema": [{"id": "TITLE", "type": "string", "display": true, "required": false, "displayName": "TITLE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CATEGORY", "type": "string", "display": true, "required": false, "displayName": "CATEGORY", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "IMAGE", "type": "string", "display": true, "required": false, "displayName": "IMAGE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SKU", "type": "string", "display": true, "removed": false, "required": false, "displayName": "SKU", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "REGULAR PRICE", "type": "string", "display": true, "removed": false, "required": false, "displayName": "REGULAR PRICE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SALE PRICE", "type": "string", "display": true, "removed": false, "required": false, "displayName": "SALE PRICE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SHORT DESCRIPTION", "type": "string", "display": true, "removed": false, "required": false, "displayName": "SHORT DESCRIPTION", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "DESCRIPTION", "type": "string", "display": true, "removed": false, "required": false, "displayName": "DESCRIPTION", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "STOCK QTY", "type": "string", "display": true, "removed": false, "required": false, "displayName": "STOCK QTY", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "DONE", "type": "string", "display": true, "required": false, "displayName": "DONE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ID", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PERMALINK", "type": "string", "display": true, "removed": false, "required": false, "displayName": "PERMALINK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["row_number"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/10k_dyLEnoFqDcKMHDrr1LwRSnQGgOrXGZCZQmf76fRU/edit#gid=0", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "1vNkSgWHsgYDCusD-xKSrQg64hd7WvOjQmqdB2NdVFG4", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1vNkSgWHsgYDCusD-xKSrQg64hd7WvOjQmqdB2NdVFG4/edit?usp=drivesdk", "cachedResultName": "Create WooCommerce products"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "de6904d1-5697-4fee-8492-e47a694e86c9", "name": "Update meta", "type": "n8n-nodes-base.googleSheets", "position": [920, -60], "parameters": {"columns": {"value": {"METATITLE": "={{ $('SEO Expert').item.json.output.metatitle }}", "row_number": "={{ $('Map categories').item.json.row_number }}", "METADESCRIPTION": "={{ $('SEO Expert').item.json.output.metadescription }}"}, "schema": [{"id": "TITLE", "type": "string", "display": true, "required": false, "displayName": "TITLE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CATEGORY", "type": "string", "display": true, "required": false, "displayName": "CATEGORY", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "IMAGE", "type": "string", "display": true, "required": false, "displayName": "IMAGE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SKU", "type": "string", "display": true, "required": false, "displayName": "SKU", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "REGULAR PRICE", "type": "string", "display": true, "required": false, "displayName": "REGULAR PRICE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SALE PRICE", "type": "string", "display": true, "required": false, "displayName": "SALE PRICE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "SHORT DESCRIPTION", "type": "string", "display": true, "required": false, "displayName": "SHORT DESCRIPTION", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "DESCRIPTION", "type": "string", "display": true, "required": false, "displayName": "DESCRIPTION", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "STOCK QTY", "type": "string", "display": true, "required": false, "displayName": "STOCK QTY", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "DONE", "type": "string", "display": true, "required": false, "displayName": "DONE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ID", "type": "string", "display": true, "required": false, "displayName": "ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "PERMALINK", "type": "string", "display": true, "required": false, "displayName": "PERMALINK", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "METATITLE", "type": "string", "display": true, "removed": false, "required": false, "displayName": "METATITLE", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "METADESCRIPTION", "type": "string", "display": true, "removed": false, "required": false, "displayName": "METADESCRIPTION", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["row_number"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1vNkSgWHsgYDCusD-xKSrQg64hd7WvOjQmqdB2NdVFG4/edit#gid=0", "cachedResultName": "Foglio1"}, "documentId": {"__rl": true, "mode": "list", "value": "1vNkSgWHsgYDCusD-xKSrQg64hd7WvOjQmqdB2NdVFG4", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1vNkSgWHsgYDCusD-xKSrQg64hd7WvOjQmqdB2NdVFG4/edit?usp=drivesdk", "cachedResultName": "Create WooCommerce products"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "JYR6a64Qecd6t8Hb", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "f9f0eb06-3d76-4675-af0a-8ec9b3811b79", "name": "Set SEO meta", "type": "n8n-nodes-base.wooCommerce", "position": [600, -60], "parameters": {"imagesUi": {}, "resource": "product", "operation": "update", "productId": "={{ $('Create product').item.json.id }}", "metadataUi": {"metadataValues": [{"key": "_yoast_wpseo_title", "value": "={{ $json.output.metatitle }}"}, {"key": "_yoast_wpseo_metadesc", "value": "={{ $json.output.metadescription }}"}]}, "dimensionsUi": {}, "updateFields": {}}, "credentials": {"wooCommerceApi": {"id": "vYYrjB5kgHQ0XByZ", "name": "WooCommerce (wp.test.7hype.com)"}}, "typeVersion": 1}, {"id": "0ae1b508-9990-4f7b-bf64-e612ef5f0396", "name": "Loop products", "type": "n8n-nodes-base.splitInBatches", "position": [120, -340], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "e713e1cd-b0cc-465d-971a-54a8002e0c39", "name": "Send message", "type": "n8n-nodes-base.telegram", "position": [400, -500], "webhookId": "9647e7ef-d449-40ff-a34d-9853e4404595", "parameters": {"text": "Product creation completed", "chatId": "CHAT_ID", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "rQ5q95W7uKesMDx4", "name": "Telegram account Fastewb"}}, "typeVersion": 1.2}, {"id": "********-8e02-4830-bf54-23096e6e1ca7", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [440, 160], "parameters": {"schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"metatitle\": {\n\t\t\t\"type\": \"string\"\n\t\t},\n\t\t\"metadescription\": {\n\t\t\t\"type\": \"string\"\n\t\t}\n\t}\n}"}, "typeVersion": 1.2}, {"id": "c46b05a5-a565-48fe-a8ad-fad34eab267c", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-460, -1060], "parameters": {"width": 800, "height": 480, "content": "## STEP 1\n- Install Yoast SEO Plugin on Wordpress\n- Add this code in function.php file\n\n```\nfunction abilita_yoast_meta_api() {\n    $meta_keys = ['_yoast_wpseo_title', '_yoast_wpseo_metadesc'];\n\n    foreach ($meta_keys as $meta_key) {\n        register_post_meta('post', $meta_key, array(\n            'type' => 'string',\n            'description' => \"Meta Yoast $meta_key per i post\",\n            'single' => true,\n            'show_in_rest' => true, \n        ));\n\n        register_post_meta('page', $meta_key, array(\n            'type' => 'string',\n            'description' => \"Meta Yoast $meta_key per le pagine\",\n            'single' => true,\n            'show_in_rest' => true,\n        ));\n    }\n}\nadd_action('init', 'abilita_yoast_meta_api');\n```"}, "typeVersion": 1}, {"id": "5cde4743-f5ab-4670-b836-d0d32b54db98", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [380, -840], "parameters": {"width": 800, "height": 260, "content": "## STEP 3\n- Copy [this sheet](https://docs.google.com/spreadsheets/d/1vNkSgWHsgYDCusD-xKSrQg64hd7WvOjQmqdB2NdVFG4/edit?usp=sharing) and add product details to be inserted in columns A to I\nIMPORTANT: \n- Columns B, E and F in \"text format\"\n- Column I in \"numeric format\"\n- Columns G and H accepts HTML\n- In Column C insert the URL of product image\n"}, "typeVersion": 1}, {"id": "cea0f8f9-2a98-465b-925c-48b9d3ea99a2", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [380, -1060], "parameters": {"width": 800, "height": 180, "content": "## STEP 2\n- Enable WooCommerce API from Wordpress\n- Add CHAT_ID in Telegram trigger"}, "typeVersion": 1}, {"id": "e61ca71a-3960-49e2-a331-b8f3df8abcd7", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-460, -1280], "parameters": {"color": 3, "width": 1640, "height": 180, "content": "# AI-Driven WooCommerce Product Importer\nThis workflow streamlines your WooCommerce product creation process by integrating directly with Google Sheets. Simply input product details into your spreadsheet, and the workflow takes care of the rest—automatically creating new products on your WooCommerce store.\n\nBut it doesn’t stop there. A dedicated SEO expert chain analyzes each product’s content and generates optimized meta titles and meta descriptions, enhancing visibility and ranking potential on search engines."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "b9e5c366-9c92-4c7d-a5aa-ee2957c94f66", "connections": {"SEO Expert": {"main": [[{"node": "Set SEO meta", "type": "main", "index": 0}]]}, "Update meta": {"main": [[{"node": "Loop products", "type": "main", "index": 0}]]}, "Get products": {"main": [[{"node": "Loop products", "type": "main", "index": 0}]]}, "Set SEO meta": {"main": [[{"node": "Update meta", "type": "main", "index": 0}]]}, "Creation done": {"main": [[{"node": "SEO Expert", "type": "main", "index": 0}]]}, "Loop products": {"main": [[{"node": "Send message", "type": "main", "index": 0}], [{"node": "Map categories", "type": "main", "index": 0}]]}, "Create product": {"main": [[{"node": "Creation done", "type": "main", "index": 0}]]}, "Map categories": {"main": [[{"node": "Create product", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "SEO Expert", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "SEO Expert", "type": "ai_outputParser", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get products", "type": "main", "index": 0}]]}}}